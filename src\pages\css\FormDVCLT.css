/* =====================================
   DVCLT Form - Modern CSS Styling
   ===================================== */

/* === ROOT VARIABLES === */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #059669;
  --success-light: #10b981;
  --warning-color: #d97706;
  --warning-light: #f59e0b;
  --danger-color: #dc2626;
  --info-color: #0891b2;
  
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* === BASE STYLES === */
* {
  box-sizing: border-box;
}

/* === MAIN CONTAINER === */
.form-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  min-height: 100vh;
}

.form-container h2 {
  color: var(--gray-800);
  margin-bottom: 2rem;
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.form-container h2::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: 2px;
}

/* === INPUT GROUPS === */
.input-group {
  margin-bottom: 1.5rem;
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: var(--transition);
}

.input-group:hover {
  box-shadow: var(--shadow);
  border-color: var(--gray-300);
}

.input-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.input-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 1rem;
  background-color: white;
  color: var(--gray-700);
  transition: var(--transition);
  cursor: pointer;
}

.input-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
  transform: translateY(-1px);
}

.input-group select:hover {
  border-color: var(--gray-300);
}

/* === BUTTON STYLES === */
.button-group {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
}

.btn-primary:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--gray-500), var(--gray-600));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* === LOG RESULTS === */
.log-results {
  margin-top: 3rem;
}

.log-results h3 {
  color: var(--gray-800);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.log-results h3::before {
  content: '📋';
  font-size: 1.25rem;
}

.log-item {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
}

.log-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.log-header {
  background: linear-gradient(135deg, var(--gray-50), white);
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-header h4 {
  margin: 0;
  color: var(--gray-800);
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.log-header h4::before {
  content: '🔍';
  font-size: 1rem;
  -webkit-text-fill-color: initial;
}

/* Style for code display in log header */
.log-header h4 {
  position: relative;
}

.log-header h4::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 1px;
  opacity: 0.6;
}

.btn-resend {
  background: linear-gradient(135deg, var(--success-color), var(--success-light));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn-resend:hover {
  background: linear-gradient(135deg, #047857, var(--success-color));
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.log-content {
  padding: 1.5rem;
}

.log-field {
  margin-bottom: 1.5rem;
  background: var(--gray-50);
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
}

.log-field:last-child {
  margin-bottom: 0;
}

.log-field label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--gray-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.log-value {
  background: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  padding: 1rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.75rem;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.5;
  box-shadow: var(--shadow-sm);
}

.log-value pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--gray-700);
}

/* === CONTROL SECTIONS === */
.resend-controls {
  background: linear-gradient(135deg, #dbeafe, #e0f2fe);
  border: 1px solid #bfdbfe;
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin: 1.5rem 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.control-group {
  background: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.control-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
  font-size: 0.875rem;
}

.control-group select,
.control-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background-color: white;
  color: var(--gray-700);
  transition: var(--transition);
}

.control-group select:focus,
.control-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.control-group textarea {
  resize: vertical;
  min-height: 3rem;
  font-family: inherit;
}

/* === JUDICIAL RESULTS === */
.judicial-results {
  margin-top: 3rem;
}

.judicial-results h3 {
  color: var(--gray-800);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.judicial-results h3::before {
  content: '⚖️';
  font-size: 1.25rem;
}

.judicial-item {
  background: white;
  border: 1px solid #10b981;
  border-radius: var(--border-radius-lg);
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
}

.judicial-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.judicial-header {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  padding: 1.5rem;
  border-bottom: 1px solid #10b981;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.judicial-header h4 {
  margin: 0;
  color: #065f46;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.judicial-header h4::before {
  content: '📄';
  font-size: 1rem;
}

.btn-sync {
  background: linear-gradient(135deg, var(--info-color), #06b6d4);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn-sync:hover:not(:disabled) {
  background: linear-gradient(135deg, #0e7490, var(--info-color));
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-sync:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.judicial-content {
  padding: 1.5rem;
}

.civil-status-controls {
  background: linear-gradient(135deg, #e0f7fa, #f0f9ff);
  border: 1px solid #67e8f9;
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin: 1.5rem 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.judicial-field {
  margin-bottom: 1.5rem;
  background: var(--gray-50);
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
}

.judicial-field:last-child {
  margin-bottom: 0;
}

.judicial-field label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #065f46;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.judicial-value {
  background: white;
  border: 1px solid #10b981;
  border-radius: var(--border-radius);
  padding: 1rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.75rem;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.5;
  box-shadow: var(--shadow-sm);
}

.judicial-value pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--gray-700);
}

/* === RESPONSE SECTION === */
.response-section {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin: 1.5rem 0;
  box-shadow: var(--shadow);
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.response-header h5 {
  margin: 0;
  color: #92400e;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.response-header h5::before {
  content: '📊';
  font-size: 1rem;
}

.response-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.btn-edit, .btn-save {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn-edit {
  background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
  color: white;
}

.btn-save {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
}

.btn-edit:hover, .btn-save:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-direct-call {
  background: linear-gradient(135deg, #ea580c, #f97316);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn-direct-call:hover:not(:disabled) {
  background: linear-gradient(135deg, #c2410c, #ea580c);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-direct-call:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

/* === CHECKBOX STYLES === */
.control-group input[type="checkbox"] {
  margin-right: 0.5rem;
  transform: scale(1.2);
  accent-color: var(--primary-color);
}

.civil-status-controls .control-group label {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  cursor: pointer;
}

/* === SYNC STATUS RESULTS === */
.sync-status-results {
  margin-top: 3rem;
}

.sync-status-results h3 {
  color: var(--gray-800);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sync-status-results h3::before {
  content: '🔄';
  font-size: 1.25rem;
}

.sync-status-item {
  background: white;
  border: 1px solid var(--info-color);
  border-radius: var(--border-radius-lg);
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
}

.sync-status-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.sync-status-header {
  background: linear-gradient(135deg, #cffafe, #a5f3fc);
  padding: 1.5rem;
  border-bottom: 1px solid var(--info-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sync-status-header h4 {
  margin: 0;
  color: #164e63;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sync-status-header h4::before {
  content: '📊';
  font-size: 1rem;
}

.btn-status-call {
  background: linear-gradient(135deg, var(--info-color), #06b6d4);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn-status-call:hover:not(:disabled) {
  background: linear-gradient(135deg, #0e7490, var(--info-color));
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-status-call:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.sync-status-content {
  padding: 1.5rem;
}

.status-call-controls {
  background: linear-gradient(135deg, #e0f7fa, #f0f9ff);
  border: 1px solid #67e8f9;
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin: 1.5rem 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.status-call-controls .control-group input[type="text"] {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 1rem;
  background-color: white;
  color: var(--gray-700);
  transition: var(--transition);
}

.status-call-controls .control-group input[type="text"]:focus {
  outline: none;
  border-color: var(--info-color);
  box-shadow: 0 0 0 3px rgb(8 145 178 / 0.1);
}

.sync-status-field {
  margin-bottom: 1.5rem;
  background: var(--gray-50);
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
}

.sync-status-field:last-child {
  margin-bottom: 0;
}

.sync-status-field label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #164e63;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.sync-status-value {
  background: white;
  border: 1px solid var(--info-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.75rem;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.5;
  box-shadow: var(--shadow-sm);
}

.sync-status-value pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--gray-700);
}

.sync-status-value .error-message {
  color: var(--danger-color);
  font-weight: 600;
}

.status-response-section {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin: 1.5rem 0;
  box-shadow: var(--shadow);
}

.status-response-section h5 {
  margin: 0 0 1rem 0;
  color: #92400e;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-response-section h5::before {
  content: '📡';
  font-size: 1rem;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
  .form-container {
    padding: 1.5rem;
  }
  
  .resend-controls {
    grid-template-columns: 1fr;
  }
  
  .civil-status-controls {
    grid-template-columns: 1fr;
  }
  
  .status-call-controls {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .form-container {
    padding: 1rem;
    margin: 0.5rem;
  }
  
  .form-container h2 {
    font-size: 1.5rem;
  }
  
  .button-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn-primary, .btn-secondary {
    justify-content: center;
  }
  
  .judicial-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .btn-sync {
    align-self: stretch;
    justify-content: center;
  }
  
  .sync-status-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .btn-status-call {
    align-self: stretch;
    justify-content: center;
  }
  
  .response-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .response-actions {
    justify-content: center;
  }
  
  .log-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .btn-resend {
    align-self: stretch;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .form-container {
    padding: 0.75rem;
  }
  
  .input-group {
    padding: 1rem;
  }
  
  .log-content, .judicial-content {
    padding: 1rem;
  }
  
  .sync-status-content {
    padding: 1rem;
  }
  
  .resend-controls, .civil-status-controls, .response-section {
    padding: 1rem;
  }
  
  .status-call-controls, .status-response-section {
    padding: 1rem;
  }
}

/* === LOADING STATES === */
.btn-primary:disabled {
  position: relative;
  overflow: hidden;
}

.btn-primary:disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* === SCROLL STYLING === */
.log-value::-webkit-scrollbar,
.judicial-value::-webkit-scrollbar,
.sync-status-value::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.log-value::-webkit-scrollbar-track,
.judicial-value::-webkit-scrollbar-track,
.sync-status-value::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 3px;
}

.log-value::-webkit-scrollbar-thumb,
.judicial-value::-webkit-scrollbar-thumb,
.sync-status-value::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 3px;
}

.log-value::-webkit-scrollbar-thumb:hover,
.judicial-value::-webkit-scrollbar-thumb:hover,
.sync-status-value::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* === FOCUS INDICATORS === */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

button:focus,
select:focus,
textarea:focus {
  outline-offset: 1px;
}

/* === PRINT STYLES === */
@media print {
  .form-container {
    box-shadow: none;
    background: white;
  }
  
  .button-group {
    display: none;
  }
  
  .response-actions {
    display: none;
  }
}

/* === MODAL STYLES === */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.modal-header h3 {
  margin: 0;
  color: var(--gray-800);
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--gray-500);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

.modal-body {
  flex: 1;
  overflow: auto;
  padding: 1.5rem;
}

.response-content {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow: auto;
  margin: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.btn-secondary:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.btn-primary:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* === SPINNER STYLES === */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === BUTTON LOADING STATES === */
.btn-resend:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-resend:disabled:hover {
  background: var(--warning-color);
  transform: none;
}

/* === RESPONSIVE MODAL === */
@media (max-width: 768px) {
  .modal-content {
    width: 95vw;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .response-content {
    font-size: 0.75rem;
    max-height: 300px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
  }
}

/* === CODE CONVERSION STYLES === */
.code-conversion-results {
  margin-top: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

.code-conversion-results h3 {
  margin: 0 0 1.5rem 0;
  color: var(--gray-800);
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.code-conversion-results h3::before {
  content: "🔄";
  font-size: 1.5rem;
}

.code-conversion-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.code-conversion-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.code-conversion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.code-conversion-header h4 {
  margin: 0;
  color: var(--gray-800);
  font-size: 1.1rem;
  font-weight: 600;
}

.btn-convert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn-convert:hover:not(:disabled) {
  background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-convert:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.code-conversion-content {
  padding: 1.5rem;
}

.conversion-controls {
  margin-bottom: 1.5rem;
}

.conversion-controls .control-group {
  margin-bottom: 1rem;
}

.conversion-controls label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
  font-weight: 500;
  font-size: 0.875rem;
}

.conversion-controls select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: white;
  transition: var(--transition);
}

.conversion-controls select:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.conversion-result {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 2px solid #22c55e;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-top: 1rem;
}

.conversion-result .result-header {
  margin-bottom: 1rem;
}

.conversion-result .result-header h5 {
  margin: 0;
  color: var(--gray-800);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.conversion-result .result-header h5::before {
  content: "✅";
  font-size: 1.2rem;
}

.result-content {
  display: grid;
  gap: 1rem;
}

.result-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.result-field label {
  color: var(--gray-600);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.result-value {
  padding: 0.75rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  font-weight: 500;
}

.result-value.success {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #22c55e;
  color: #15803d;
}

.result-value.error {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-color: #ef4444;
  color: #dc2626;
}

/* === RESPONSIVE CODE CONVERSION === */
@media (max-width: 768px) {
  .code-conversion-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .btn-convert {
    width: 100%;
    justify-content: center;
  }

  .result-content {
    grid-template-columns: 1fr;
  }
}